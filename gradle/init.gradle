// Gradle 初始化脚本 - 完全禁用SSL验证（仅开发环境）

allprojects {
    repositories.all { ArtifactRepository repo ->
        if (repo instanceof MavenArtifactRepository) {
            MavenArtifactRepository maven = repo as MavenArtifactRepository
            maven.allowInsecureProtocol = true
        }
    }
}

// 完全禁用SSL验证
System.setProperty("javax.net.ssl.trustStore", "")
System.setProperty("javax.net.ssl.trustStoreType", "")
System.setProperty("javax.net.ssl.trustStorePassword", "")
System.setProperty("javax.net.ssl.keyStore", "")
System.setProperty("javax.net.ssl.keyStoreType", "")
System.setProperty("javax.net.ssl.keyStorePassword", "")
System.setProperty("com.sun.net.ssl.checkRevocation", "false")
System.setProperty("sun.security.ssl.allowUnsafeRenegotiation", "true")
System.setProperty("sun.security.ssl.allowLegacyHelloMessages", "true")
System.setProperty("org.gradle.internal.repository.transport.http.allowInsecureProtocol", "true")

// 禁用主机名验证
import javax.net.ssl.*
import java.security.cert.X509Certificate

// 创建信任所有证书的TrustManager
def trustAllCerts = [
    new X509TrustManager() {
        public X509Certificate[] getAcceptedIssuers() { return null }
        public void checkClientTrusted(X509Certificate[] certs, String authType) { }
        public void checkServerTrusted(X509Certificate[] certs, String authType) { }
    }
] as TrustManager[]

// 创建允许所有主机名的HostnameVerifier
def allHostsValid = new HostnameVerifier() {
    public boolean verify(String hostname, SSLSession session) { return true }
}

try {
    // 安装信任所有证书的TrustManager
    SSLContext sc = SSLContext.getInstance("SSL")
    sc.init(null, trustAllCerts, new java.security.SecureRandom())
    HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory())
    
    // 安装允许所有主机名的HostnameVerifier
    HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid)
    
    println "SSL验证已禁用 - 仅用于开发环境"
} catch (Exception e) {
    println "警告: 无法禁用SSL验证: ${e.message}"
}
